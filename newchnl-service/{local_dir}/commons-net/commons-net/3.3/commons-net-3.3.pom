<?xml version="1.0" encoding="UTF-8"?>
<!--

   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-parent</artifactId>
        <version>30</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>commons-net</groupId>
    <artifactId>commons-net</artifactId>
    <version>3.3</version>
    <name>Commons Net</name>
    <!-- N.B. the description content is deliberately not indented
     ! to improve the layout of the Release Notes generated by mvn changes:announcement-generate
    -->
    <description>
Apache Commons Net library contains a collection of network utilities and protocol implementations.
Supported protocols include: Echo, Finger, FTP, NNTP, NTP, POP3(S), SMTP(S), Telnet, Whois
    </description>
    <url>http://commons.apache.org/proper/commons-net/</url>
    <issueManagement>
        <system>jira</system>
        <url>http://issues.apache.org/jira/browse/NET</url>
    </issueManagement>
    <inceptionYear>2001</inceptionYear>
    <scm>
        <connection>scm:svn:http://svn.apache.org/repos/asf/commons/proper/net/trunk</connection>
        <developerConnection>scm:svn:https://svn.apache.org/repos/asf/commons/proper/net/trunk</developerConnection>
        <url>http://svn.apache.org/viewvc/commons/proper/net/trunk</url>
    </scm>

    <developers>
        <developer>
            <name>Jeffrey D. Brekke</name>
            <id>brekke</id>
            <email><EMAIL></email>
            <organization>Quad/Graphics, Inc.</organization>
        </developer>
        <developer>
            <name>Steve Cohen</name>
            <id>scohen</id>
            <email><EMAIL></email>
            <organization>javactivity.org</organization>
        </developer>
        <developer>
            <name>Bruno D'Avanzo</name>
            <id>brudav</id>
            <email><EMAIL></email>
            <organization>Hewlett-Packard</organization>
        </developer>
        <developer>
            <name>Daniel F. Savarese</name>
            <id>dfs</id>
            <email><EMAIL></email>
            <organization>
                &lt;a href="http://www.savarese.com/"&gt;Savarese Software Research&lt;/a&gt;
            </organization>
        </developer>
        <developer>
            <name>Rory Winston</name>
            <id>rwinston</id>
            <email><EMAIL></email>
            <organization />
        </developer>
    </developers>

    <contributors>
        <contributor>
            <name>Rob Hasselbaum</name>
            <email><EMAIL></email>
        </contributor>
        <contributor>
            <name>Mario Ivankovits</name>
            <email><EMAIL></email>
        </contributor>
        <contributor>
            <name>Tapan Karecha</name>
            <email><EMAIL></email>
        </contributor>
        <contributor>
            <name>Winston Ojeda</name>
            <email><EMAIL></email>
            <organization>Quad/Graphics, Inc.</organization>
        </contributor>
        <contributor>
            <name>Ted Wise</name>
            <email><EMAIL></email>
        </contributor>
        <contributor>
            <name>Bogdan Drozdowski</name>
            <email>bogdandr # op dot pl</email>
        </contributor>
    </contributors>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.11</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <distributionManagement>
      <site>
        <id>apache.website</id>
        <name>Apache Commons Site</name>
        <url>${commons.deployment.protocol}://people.apache.org/www/commons.apache.org/${commons.componentid}</url>
      </site>
    </distributionManagement>

    <properties>
        <maven.compile.source>1.5</maven.compile.source>
        <maven.compile.target>1.5</maven.compile.target>
        <commons.javadoc.java.link>http://download.oracle.com/javase/1.5.0/docs/api/</commons.javadoc.java.link>
        <commons.componentid>net</commons.componentid>
        <commons.release.version>3.3</commons.release.version>
        <commons.rc.version>RC1</commons.rc.version>
        <commons.release.desc>(Requires Java 1.5 or later)</commons.release.desc>
        <commons.release.2.version>1.4.1</commons.release.2.version>
        <!-- Override Commons parent, as version 1.4.1 does not use the -bin suffix -->
        <commons.release.2.binary.suffix />
        <commons.release.2.desc>(Requires Java 1.3 or later)</commons.release.2.desc>
        <commons.jira.id>NET</commons.jira.id>
        <commons.jira.pid>12310487</commons.jira.pid>
    </properties>

    <build>
        <plugins>

            <!-- Exclude examples from binary jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>examples/**</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <!-- Exclude examples from source jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>examples/**</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>**/*FunctionalTest.java</exclude>
                        <exclude>**/*POP3*.java</exclude>
                        <!-- JUnit4 and/or Surefire does not like a class with a private constructor -->
                        <exclude>**/TestSetupParameters.java</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <descriptors>
                        <descriptor>src/main/assembly/bin.xml</descriptor>
                        <descriptor>src/main/assembly/src.xml</descriptor>
                    </descriptors>
                    <tarLongFileMode>gnu</tarLongFileMode>
                </configuration>
            </plugin>

            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <configuration>
                            <target>
                                <jar destfile="target/commons-net-ftp-${project.version}.jar">
                                    <metainf dir="${basedir}" includes="NOTICE.txt,LICENSE.txt" />
                                    <manifest>
                                        <attribute name="Extension-Name" value="org.apache.commons.net" />
                                        <attribute name="Specification-Title" value="${project.name}" />
                                        <attribute name="Implementation-Title" value="${project.name}" />
                                        <attribute name="Implementation-Vendor" value="${project.organization.name}" />
                                        <attribute name="Implementation-Version" value="${project.version}" />
                                        <attribute name="Implementation-Vendor-Id" value="org.apache" />
                                        <attribute name="Implementation-Build" value="${implementation.build}"/>
                                        <attribute name="X-Compile-Source-JDK" value="${maven.compile.source}" />
                                        <attribute name="X-Compile-Target-JDK" value="${maven.compile.target}" />
                                    </manifest>
                                    <fileset dir="target/classes" includes="org/apache/commons/net/ftp/**,org/apache/commons/net/*,org/apache/commons/net/io/*,org/apache/commons/net/util/*" />
                                </jar>
                                <!--
                                    Create the binary examples jar, which will be added to the binary zip/tgz,
                                    but not deployed independently to Maven
                                -->
                                <jar destfile="target/commons-net-examples-${project.version}.jar">
                                    <metainf dir="${basedir}" includes="NOTICE.txt,LICENSE.txt" />
                                    <manifest>
                                        <attribute name="Extension-Name" value="org.apache.commons.net" />
                                        <attribute name="Specification-Title" value="${project.name}" />
                                        <attribute name="Implementation-Title" value="${project.name}" />
                                        <attribute name="Implementation-Vendor" value="${project.organization.name}" />
                                        <attribute name="Implementation-Version" value="${project.version}" />
                                        <attribute name="Implementation-Vendor-Id" value="org.apache" />
                                        <attribute name="Implementation-Build" value="${implementation.build}"/>
                                        <attribute name="X-Compile-Source-JDK" value="${maven.compile.source}" />
                                        <attribute name="X-Compile-Target-JDK" value="${maven.compile.target}" />
                                        <!-- Helper application -->
                                        <attribute name="Main-Class" value="examples/Main" />
                                        <!-- Allow java -jar examples.jar to work -->
                                        <attribute name="Class-Path" value="commons-net-${project.version}.jar" />
                                    </manifest>
                                    <fileset dir="target/classes" includes="examples/**" />
                                </jar>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--  Attaches the commons-net-ftp and examples JARs to the Maven lifecycle
                  to ensure they will be signed and deployed as normal -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>1.7</version>
                <executions>
                    <execution>
                        <id>attach-artifacts</id>
                        <phase>package</phase>
                        <goals>
                            <goal>attach-artifact</goal>
                        </goals>
                        <configuration>
                            <artifacts>
                                <artifact>
                                    <file>target/commons-net-ftp-${project.version}.jar</file>
                                    <type>jar</type>
                                    <classifier>ftp</classifier>
                                </artifact>
                                <artifact>
                                    <file>target/commons-net-examples-${project.version}.jar</file>
                                    <type>jar</type>
                                    <classifier>examples</classifier>
                                </artifact>
                            </artifacts>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Exclude examples from Javadoc jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <excludePackageNames>examples.*</excludePackageNames>
                </configuration>
            </plugin>

            <!-- Copy the examples sources -->
            <plugin>
              <artifactId>maven-resources-plugin</artifactId>
              <executions>
                <execution>
                  <id>copy-resources</id>
                  <phase>pre-site</phase>
                  <goals>
                    <goal>copy-resources</goal>
                  </goals>
                  <configuration>
                    <outputDirectory>${basedir}/target/site/examples</outputDirectory>
                    <resources>
                      <resource>
                        <directory>src/main/java/examples</directory>
                        <excludes>
                          <exclude>**/Main.java</exclude>
                        </excludes>
                        <filtering>false</filtering>
                      </resource>
                    </resources>
                  </configuration>
                </execution>
              </executions>
            </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-scm-publish-plugin</artifactId>
            <configuration>
              <ignorePathsToDelete>
                <ignorePathToDelete>javadocs</ignorePathToDelete>
              </ignorePathsToDelete>
            </configuration>
          </plugin>

        </plugins>

    </build>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-changes-plugin</artifactId>
                <version>${commons.changes.version}</version>
                <configuration>
                    <issueLinkTemplatePerSystem>
                        <default>%URL%/%ISSUE%</default>
                    </issueLinkTemplatePerSystem>
                    <template>release-notes.vm</template>
                    <templateDirectory>src/changes</templateDirectory>
                    <!--  Add sample JIRA report - 'mvn changes:jira-report' or 'mvn site' -->
                    <onlyCurrentVersion>false</onlyCurrentVersion>
                    <columnNames>Fix Version,Key,Component,Summary,Type,Resolution,Status</columnNames>
                    <!-- Sort cols have to be reversed in JIRA 4 -->
                    <sortColumnNames>Key DESC,Type,Fix Version DESC</sortColumnNames>
                    <resolutionIds>Fixed</resolutionIds>
                    <statusIds>Resolved,Closed</statusIds>
                    <!-- Don't include sub-task -->
                    <typeIds>Bug,New Feature,Task,Improvement,Wish,Test</typeIds>
                    <fixVersionIds>${commons.release.version}</fixVersionIds>
                    <!-- The default is 100 -->
                    <maxEntries>100</maxEntries>
                </configuration>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>changes-report</report>
                            <report>jira-report</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>2.5.2</version>
                <configuration>
                    <excludeFilterFile>findbugs-exclude-filter.xml</excludeFilterFile>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>clirr-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                      <exclude>examples/**</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <excludePackageNames>examples.*</excludePackageNames>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>2.10</version>
                <configuration>
                    <configLocation>${basedir}/checkstyle.xml</configLocation>
                    <suppressionsLocation>${basedir}/checkstyle-suppressions.xml</suppressionsLocation>
                    <enableRulesSummary>false</enableRulesSummary>
                </configuration>
            </plugin>

            <plugin>
              <groupId>org.apache.rat</groupId>
              <artifactId>apache-rat-plugin</artifactId>
              <!-- Parent pom does not (yet) exclude these -->
              <configuration>
                <excludes>
                  <exclude>.checkstyle</exclude>
                  <exclude>.fbprefs</exclude>
                </excludes>
              </configuration>      
            </plugin>

        </plugins>
    </reporting>

</project>

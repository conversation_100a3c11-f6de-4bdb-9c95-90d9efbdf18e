<?xml version="1.0"?>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-parent</artifactId>
    <version>24</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>commons-io</groupId>
  <artifactId>commons-io</artifactId>
  <version>2.2</version>
  <name>Commons IO</name>

  <inceptionYear>2002</inceptionYear>
  <description>
The Commons IO library contains utility classes, stream implementations, file filters, 
file comparators, endian transformation classes, and much more.
  </description>

  <url>http://commons.apache.org/io/</url>

  <issueManagement>
    <system>jira</system>
    <url>http://issues.apache.org/jira/browse/IO</url>
  </issueManagement>

  <distributionManagement>
    <site>
      <id>apache.website</id>
      <name>Apache Commons IO Site</name>
      <url>${commons.deployment.protocol}://people.apache.org/www/commons.apache.org/${commons.componentid}</url>
    </site>
  </distributionManagement>

  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/commons/proper/io/trunk</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/commons/proper/io/trunk</developerConnection>
    <url>http://svn.apache.org/viewvc/commons/proper/io/trunk</url>
  </scm>

  <developers>
    <developer>
      <name>Scott Sanders</name>
      <id>sanders</id>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>dIon Gillard</name>
      <id>dion</id>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Nicola Ken Barozzi</name>
      <id>nicolaken</id>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Henri Yandell</name>
      <id>bayard</id>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Stephen Colebourne</name>
      <id>scolebourne</id>
      <organization></organization>
      <roles>
        <role>Java Developer</role>
      </roles>
      <timezone>0</timezone>
    </developer>
    <developer>
      <name>Jeremias Maerki</name>
      <id>jeremias</id>
      <email><EMAIL></email>
      <organization />
      <roles>
        <role>Java Developer</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <name>Matthew Hawthorne</name>
      <id>matth</id>
      <email><EMAIL></email>
      <organization />
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Martin Cooper</name>
      <id>martinc</id>
      <email><EMAIL></email>
      <organization />
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Rob Oxspring</name>
      <id>roxspring</id>
      <email><EMAIL></email>
      <organization />
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Jochen Wiedmann</name>
      <id>jochen</id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Niall Pemberton</name>
      <id>niallp</id>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Jukka Zitting</name>
      <id>jukka</id>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <name>Gary Gregory</name>
      <id>ggregory</id>
      <email><EMAIL></email>
      <url>http://www.garygregory.com</url>
      <timezone>-5</timezone>
    </developer>
  </developers>

  <contributors>
    <contributor>
      <name>Rahul Akolkar</name>
    </contributor>
    <contributor>
      <name>Jason Anderson</name>
    </contributor>
    <contributor>
      <name>Nathan Beyer</name>
    </contributor>
    <contributor>
      <name>Emmanuel Bourg</name>
    </contributor>
    <contributor>
      <name>Chris Eldredge</name>
    </contributor>
    <contributor>
      <name>Magnus Grimsell</name>
    </contributor>
    <contributor>
      <name>Jim Harrington</name>
    </contributor>
    <contributor>
      <name>Thomas Ledoux</name>
    </contributor>
    <contributor>
      <name>Andy Lehane</name>
    </contributor>
    <contributor>
      <name>Marcelo Liberato</name>
    </contributor>
    <contributor>
      <name>Alban Peignier</name>
      <email>alban.peignier at free.fr</email>
    </contributor>
    <contributor>
      <name>Ian Springer</name>
    </contributor>
    <contributor>
      <name>Masato Tezuka</name>
    </contributor>
    <contributor>
      <name>James Urie</name>
    </contributor>
    <contributor>
      <name>Frank W. Zammetti</name>
    </contributor>
  </contributors>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.10</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <properties>
    <maven.compile.source>1.5</maven.compile.source>
    <maven.compile.target>1.5</maven.compile.target>
    <commons.componentid>io</commons.componentid>
    <commons.release.version>2.2</commons.release.version>
    <commons.rc.version>RC1</commons.rc.version>
    <commons.release.desc>(requires JDK 1.5+)</commons.release.desc>
    <commons.release.2.version>1.4</commons.release.2.version>
    <commons.release.2.desc>(requires JDK 1.3+)</commons.release.2.desc>
    <commons.jira.id>IO</commons.jira.id>
    <commons.jira.pid>12310477</commons.jira.pid>
    <!-- temporary override of parent -->
    <commons.clirr.version>2.4</commons.clirr.version>
  </properties>

  <build>
    <pluginManagement>
      <plugins>
        <!-- Temporarily needed until CP25 is available -->
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <version>${commons.clirr.version}</version>
          <configuration>
            <minSeverity>${minSeverity}</minSeverity>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <forkMode>pertest</forkMode>
          <!-- limit memory size see IO-161 -->
          <argLine>-Xmx25M</argLine>
          <includes>
            <!-- Only include test classes, not test data -->
            <include>**/*Test*.class</include>
          </includes>
          <excludes>
            <exclude>**/*AbstractTestCase*</exclude>
            <exclude>**/testtools/**</exclude>

            <!-- http://jira.codehaus.org/browse/SUREFIRE-44 -->
            <exclude>**/*$*</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <descriptors>
            <descriptor>src/main/assembly/bin.xml</descriptor>
            <descriptor>src/main/assembly/src.xml</descriptor>
          </descriptors>
          <tarLongFileMode>gnu</tarLongFileMode>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>2.9.1</version>
        <configuration>
          <configLocation>${basedir}/checkstyle.xml</configLocation>
          <enableRulesSummary>false</enableRulesSummary>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>2.4.0</version>
        <configuration>
          <threshold>Normal</threshold>
          <effort>Default</effort>
          <excludeFilterFile>${basedir}/findbugs-exclude-filter.xml</excludeFilterFile>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-changes-plugin</artifactId>
        <version>${commons.changes.version}</version>
        <configuration>
          <xmlPath>${basedir}/src/changes/changes.xml</xmlPath>
          <columnNames>Fix Version,Key,Component,Summary,Type,Resolution,Status</columnNames>
          <!-- Sort cols have to be reversed in JIRA 4 -->
          <sortColumnNames>Key DESC,Type,Fix Version DESC</sortColumnNames>
          <resolutionIds>Fixed</resolutionIds>
          <statusIds>Resolved,Closed</statusIds>
          <!-- Don't include sub-task -->
          <typeIds>Bug,New Feature,Task,Improvement,Wish,Test</typeIds>
          <maxEntries>300</maxEntries>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>changes-report</report>
              <report>jira-report</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>src/test/resources/**/*.bin</exclude>
            <exclude>.pmd</exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </reporting>
</project>

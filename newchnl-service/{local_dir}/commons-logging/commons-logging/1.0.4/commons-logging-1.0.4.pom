<project>
  <modelVersion>4.0.0</modelVersion>
  <groupId>commons-logging</groupId>
  <artifactId>commons-logging</artifactId>
  <name>Logging</name>
  <version>1.0.4</version>
  <description>Commons Logging is a thin adapter allowing configurable bridging to other,
    well known logging systems.</description>
  <url>http://jakarta.apache.org/commons/logging/</url>
  <issueManagement>
    <url>http://issues.apache.org/bugzilla/</url>
  </issueManagement>
  <ciManagement>
    <notifiers>
      <notifier>
        <configuration>
          <address><EMAIL></address>
        </configuration>
      </notifier>
    </notifiers>
  </ciManagement>
  <inceptionYear>2001</inceptionYear>
  <mailingLists>
    <mailingList>
      <name>Commons Dev List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://nagoya.apache.org/eyebrowse/SummarizeList?listName=<EMAIL></archive>
    </mailingList>
    <mailingList>
      <name>Commons User List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <archive>http://nagoya.apache.org/eyebrowse/SummarizeList?listName=<EMAIL></archive>
    </mailingList>
  </mailingLists>
  <developers>
    <developer>
      <id>morgand</id>
      <name>Morgan Delagrange</name>
      <email>morgand at apache dot org</email>
      <organization>Apache</organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
    <developer>
      <id>rwaldhoff</id>
      <name>Rodney Waldhoff</name>
      <email>rwaldhoff at apache org</email>
      <organization>Apache Software Foundation</organization>
    </developer>
    <developer>
      <id>craigmcc</id>
      <name>Craig McClanahan</name>
      <email>craigmcc at apache org</email>
      <organization>Apache Software Foundation</organization>
    </developer>
    <developer>
      <id>sanders</id>
      <name>Scott Sanders</name>
      <email>sanders at apache dot org</email>
      <organization>Apache Software Foundation</organization>
    </developer>
    <developer>
      <id>rdonkin</id>
      <name>Robert Burrell Donkin</name>
      <email>rdonkin at apache dot org</email>
      <organization>Apache Software Foundation</organization>
    </developer>
    <developer>
      <id>donaldp</id>
      <name>Peter Donald</name>
      <email>donaldp at apache dot org</email>
      <organization></organization>
    </developer>
    <developer>
      <id>costin</id>
      <name>Costin Manolache</name>
      <email>costin at apache dot org</email>
      <organization>Apache Software Foundation</organization>
    </developer>
    <developer>
      <id>rsitze</id>
      <name>Richard Sitze</name>
      <email>rsitze at apache dot org</email>
      <organization>Apache Software Foundation</organization>
    </developer>
    <developer>
      <id>baliuka</id>
      <name>Juozas Baliuka</name>
      <email><EMAIL></email>
      <organization></organization>
      <roles>
        <role>Java Developer</role>
      </roles>
    </developer>
  </developers>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>/LICENSE.txt</url>
    </license>
  </licenses>
  <scm>
    <connection>scm:cvs:pserver:<EMAIL>:/home/<USER>/logging</connection>
    <url>http://cvs.apache.org/viewcvs/jakarta-commons/logging/</url>
  </scm>
  <organization>
    <name>The Apache Software Foundation</name>
    <url>http://jakarta.apache.org</url>
  </organization>
  <build>
    <sourceDirectory>src/java</sourceDirectory>
    <testSourceDirectory>src/test</testSourceDirectory>
    <plugins>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <includes>
            <include>**/AvalonLoggerTest.java</include>
          </includes>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>log4j</groupId>
      <artifactId>log4j</artifactId>
      <version>1.2.6</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>logkit</groupId>
      <artifactId>logkit</artifactId>
      <version>1.0.1</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.7</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>avalon-framework</groupId>
      <artifactId>avalon-framework</artifactId>
      <version>4.1.3</version>
      <optional>true</optional>
    </dependency>
  </dependencies>
  <distributionManagement>
    <repository>
      <id>default</id>
      <name>Default Repository</name>
      <url>file:///www/jakarta.apache.org/builds/jakarta-commons/logging/</url>
    </repository>
    <site>
      <id>default</id>
      <name>Default Site</name>
      <url>scp://jakarta.apache.org//www/jakarta.apache.org/commons/logging/</url>
    </site>
  </distributionManagement>
</project>
